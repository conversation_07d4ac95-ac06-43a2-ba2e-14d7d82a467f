# Custom Date Picker Component - Complete Guide

## Overview

The `custom-date-picker` is a comprehensive, reusable Blade component that provides European date format (DD/MM/YYYY) functionality using Flatpickr. It features a Boxicons calendar icon, Bootstrap's success green color scheme, full dark mode compatibility, robust error handling, and extensive customization options for consistent date input across the Laravel application.

## Component Architecture

### Files Structure
- **Component Class**: `app/View/Components/CustomDatePicker.php`
- **Blade Template**: `resources/views/components/custom-date-picker.blade.php`
- **Test Page**: `resources/views/test/custom-date-picker.blade.php`
- **Test Route**: `/test/custom-date-picker` (admin access required)

### Integration Points
- **Reservation Forms**: `resources/views/reservations/create.blade.php` and `edit.blade.php`
- **Admin Layout**: Flatpickr library included in `resources/views/layouts/admin.blade.php`

## Features

### ✅ Core Functionality
- **European Date Format**: Displays dates in DD/MM/YYYY format
- **Server Compatibility**: Sends ISO format (YYYY-MM-DD) to server
- **Manual Input**: Supports typing dates in multiple European formats
- **Past Dates**: Allows selection of past dates by default
- **Calendar Picker**: Enhanced Flatpickr integration with calendar interface

### ✅ Laravel Integration
- **Validation Support**: Full integration with Laravel validation system
- **Error Display**: Automatic validation error display with Bootstrap styling
- **Old Input**: Supports Laravel's old input value restoration
- **Error Bags**: Custom error bag handling for complex forms

### ✅ Visual Design
- **Bootstrap Styling**: Consistent with Bootstrap form controls
- **Success Green Theme**: Uses Bootstrap's success color (#198754)
- **Calendar Icon**: Boxicons calendar icon for intuitive date picker indication
- **Icon Fallback System**: Robust fallback hierarchy (Boxicons → Tabler → Bootstrap → Unicode)
- **Dark Mode**: Full compatibility with admin theme dark mode

### ✅ Accessibility & UX
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Readers**: Proper labeling and ARIA attributes
- **Visual Hierarchy**: Subtle today indicator, prominent selected dates
- **Responsive**: Works across all device sizes

### ✅ Developer Experience
- **Extensive Props**: Comprehensive configuration options
- **Event Handling**: Custom onChange callback support
- **JavaScript API**: Programmatic control functions
- **Documentation**: Complete usage examples and API reference

## Icon Implementation

### Calendar Icon System

The component uses a Boxicons calendar icon as the primary visual indicator, with a comprehensive fallback system:

**Primary Icon: Boxicons Calendar**
- **Unicode**: `\f237`
- **Font Family**: `boxicons`
- **Visual**: 📅 (calendar icon)
- **Purpose**: Clear indication of date picker functionality

**Fallback Hierarchy:**
1. **Boxicons Calendar** (`\f237`) - Primary choice
2. **Tabler Icons Chevron** (`\ea5f`) - First fallback
3. **Bootstrap Icons Chevron** (`\f282`) - Second fallback
4. **Unicode Calendar Emoji** (`📅`) - Ultimate fallback

**Automatic Detection:**
The component automatically detects which icon fonts are available and applies the appropriate fallback class:
- `.tabler-fallback` - When Boxicons unavailable but Tabler Icons available
- `.bootstrap-icons-fallback` - When both Boxicons and Tabler unavailable
- `.unicode-fallback` - When all icon fonts fail to load

## Error Handling & Fixes

### JavaScript Error Resolution

**Issue Resolved**: "Invalid date provided: 2025-08-24" error when initializing with ISO format dates.

**Solution**: Enhanced `parseDate` function to handle both ISO (YYYY-MM-DD) and European (DD/MM/YYYY) formats:

```javascript
config.parseDate = function(datestr, format) {
    // Handle ISO format YYYY-MM-DD (for defaultDate and server values)
    const isoRegex = /^(\d{4})-(\d{1,2})-(\d{1,2})$/;
    const isoMatch = datestr.match(isoRegex);

    if (isoMatch) {
        const year = parseInt(isoMatch[1], 10);
        const month = parseInt(isoMatch[2], 10) - 1;
        const day = parseInt(isoMatch[3], 10);

        if (year >= 1900 && month >= 0 && month <= 11 && day >= 1 && day <= 31) {
            return new Date(year, month, day);
        }
    }

    // Handle European format DD/MM/YYYY or DD.MM.YYYY or DD-MM-YYYY
    const europeanRegex = /^(\d{1,2})[\/\.\-](\d{1,2})[\/\.\-](\d{4})$/;
    const europeanMatch = datestr.match(europeanRegex);

    if (europeanMatch) {
        const day = parseInt(europeanMatch[1], 10);
        const month = parseInt(europeanMatch[2], 10) - 1;
        const year = parseInt(europeanMatch[3], 10);

        if (day >= 1 && day <= 31 && month >= 0 && month <= 11 && year >= 1900) {
            return new Date(year, month, day);
        }
    }

    return undefined; // Let Flatpickr use default parsing
};
```

### Initial Value Display Fix

**Issue Resolved**: Component not displaying initial values in European format on page load.

**Solution**: Added `defaultDate` configuration during Flatpickr initialization:

```javascript
// Get initial value from the input
const initialValue = input.value;

// Set default date if initial value exists
if (initialValue && initialValue.trim() !== '') {
    // Validate the date before setting it as defaultDate
    const testDate = new Date(initialValue);
    if (!isNaN(testDate.getTime())) {
        config.defaultDate = initialValue;
        console.log('Setting defaultDate for', input.id, ':', initialValue);
    } else {
        console.warn('Invalid initial date value for', input.id, ':', initialValue);
    }
}
```

**Comprehensive Error Handling:**
- Fallback initialization if `defaultDate` causes errors
- Graceful degradation with multiple retry strategies
- Detailed console logging for debugging

## Props Reference

### Required Props

| Prop | Type | Description |
|------|------|-------------|
| `name` | string | The input field name attribute |
| `id` | string | The input field id attribute |

### Optional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | string | null | Default/current value in Y-m-d format |
| `placeholder` | string | "DD/MM/YYYY" | Placeholder text |
| `required` | boolean | false | Required validation |
| `minDate` | string | null | Minimum selectable date (optional) |
| `disabled` | boolean | false | Disable the input |
| `class` | string | "" | Additional CSS classes |
| `errorBag` | string | null | Laravel error bag name |
| `onChange` | string | null | JavaScript callback function |
| `label` | string | null | Field label text |
| `showLabel` | boolean | true | Whether to show the label |

## Usage Examples

### Basic Implementation
```blade
<x-custom-date-picker 
    name="booking_date" 
    id="booking_date" 
    label="Date" />
```

### With Validation and Events
```blade
<x-custom-date-picker 
    name="booking_date" 
    id="booking_date" 
    :value="old('booking_date', $selectedDate ?: date('Y-m-d'))"
    label="Booking Date"
    :required="true"
    :on-change="'loadAvailability(); checkUtilityPrerequisites(); enableProgressiveFields();'" />
```

### Disabled State
```blade
<x-custom-date-picker 
    name="readonly_date" 
    id="readonly_date" 
    label="Read Only Date"
    :disabled="true"
    :value="$existingDate" />
```

### Custom Min Date
```blade
<x-custom-date-picker 
    name="future_date" 
    id="future_date" 
    label="Future Date Only"
    min-date="today" />
```

### Manual Input Friendly
```blade
<x-custom-date-picker 
    name="any_date" 
    id="any_date" 
    label="Any Date"
    placeholder="DD/MM/YYYY - Type or click" />
```

### Without Label
```blade
<x-custom-date-picker 
    name="no_label" 
    id="no_label" 
    :show-label="false"
    placeholder="Select date..." />
```

## Manual Input Formats

The component supports manual typing in multiple European date formats:

- **DD/MM/YYYY**: `25/12/2024`, `01/01/2025`
- **DD.MM.YYYY**: `25.12.2024`, `01.01.2025`
- **DD-MM-YYYY**: `25-12-2024`, `01-01-2025`

All formats are automatically validated and converted to ISO format (YYYY-MM-DD) for server submission.

## JavaScript API

### Global Functions

```javascript
// Reinitialize all date pickers (useful for dynamic content)
reinitializeCustomDatePickers();

// Get date picker instance
const picker = getCustomDatePicker('booking_date');

// Set date programmatically
setCustomDatePickerValue('booking_date', '2024-12-25');

// Clear date picker
clearCustomDatePicker('booking_date');
```

### Direct Flatpickr Access

```javascript
// Access the underlying Flatpickr instance
const element = document.getElementById('booking_date');
const flatpickrInstance = element._flatpickr;

// Use Flatpickr methods directly
flatpickrInstance.setDate('2024-12-25');
flatpickrInstance.clear();
flatpickrInstance.open();
flatpickrInstance.close();
```

## Styling & Theming

### Color Scheme

The component uses Bootstrap's success green color scheme:

- **Today Indicator**: `rgba(25, 135, 84, 0.3)` (30% opacity)
- **Selected Date**: `#198754` (solid success green)
- **Hover Effects**: `rgba(25, 135, 84, 0.1)` (light green)
- **Focus Border**: `#198754` with green shadow

### Light Mode Styling

```css
/* Today date indicator - subtle green */
.flatpickr-calendar .flatpickr-day.today {
    background-color: rgba(25, 135, 84, 0.3) !important;
    color: #495057 !important;
    border-color: rgba(25, 135, 84, 0.5) !important;
}

/* Selected date - prominent green */
.flatpickr-calendar .flatpickr-day.selected {
    background-color: #198754 !important;
    color: #fff !important;
    border-color: #198754 !important;
}

/* Container styling */
.custom-date-picker-wrapper {
    position: relative;
    border-radius: 0.375rem;
}

/* Calendar icon styling - Primary Boxicons implementation */
.custom-date-picker-wrapper .flatpickr-input::after,
.custom-date-picker-wrapper::after {
    content: '\f237'; /* Boxicons calendar */
    font-family: 'boxicons';
    font-weight: normal;
    position: absolute;
    right: 12px;
    top: 70%; /* Adjusted for better vertical alignment */
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
    z-index: 1;
    font-size: 0.875rem;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Fallback icon implementations */
.custom-date-picker-wrapper .flatpickr-input.tabler-fallback::after,
.custom-date-picker-wrapper.tabler-fallback::after {
    content: '\ea5f'; /* Tabler Icons chevron-down */
    font-family: 'tabler-icons';
}

.custom-date-picker-wrapper .flatpickr-input.bootstrap-icons-fallback::after,
.custom-date-picker-wrapper.bootstrap-icons-fallback::after {
    content: '\f282'; /* Bootstrap Icons chevron-down */
    font-family: 'bootstrap-icons';
}

.custom-date-picker-wrapper .flatpickr-input.unicode-fallback::after,
.custom-date-picker-wrapper.unicode-fallback::after {
    content: '📅'; /* Calendar emoji fallback */
    font-family: sans-serif;
    font-size: 0.875rem;
}
```

### Dark Mode Compatibility

The component includes comprehensive dark mode support using the `[data-theme-mode="dark"]` selector:

```css
/* Calendar container in dark mode */
[data-theme-mode="dark"] .flatpickr-calendar {
    background-color: #212529 !important;
    border-color: #dee2e6 !important;
    color: #fff !important;
}

/* Date cells in dark mode */
[data-theme-mode="dark"] .flatpickr-calendar .flatpickr-day.today {
    color: #fff !important;
}

/* Calendar icon in dark mode - all variants */
[data-theme-mode="dark"] .custom-date-picker-wrapper .flatpickr-input::after,
[data-theme-mode="dark"] .custom-date-picker-wrapper::after,
[data-theme-mode="dark"] .custom-date-picker-wrapper .flatpickr-input.tabler-fallback::after,
[data-theme-mode="dark"] .custom-date-picker-wrapper.tabler-fallback::after,
[data-theme-mode="dark"] .custom-date-picker-wrapper .flatpickr-input.bootstrap-icons-fallback::after,
[data-theme-mode="dark"] .custom-date-picker-wrapper.bootstrap-icons-fallback::after,
[data-theme-mode="dark"] .custom-date-picker-wrapper .flatpickr-input.unicode-fallback::after,
[data-theme-mode="dark"] .custom-date-picker-wrapper.unicode-fallback::after {
    color: #adb5bd !important;
}

/* Disabled state in all themes */
.custom-date-picker-wrapper .flatpickr-input.disabled::after,
.custom-date-picker-wrapper.disabled::after,
.custom-date-picker-wrapper .flatpickr-input.tabler-fallback.disabled::after,
.custom-date-picker-wrapper.tabler-fallback.disabled::after,
.custom-date-picker-wrapper .flatpickr-input.bootstrap-icons-fallback.disabled::after,
.custom-date-picker-wrapper.bootstrap-icons-fallback.disabled::after,
.custom-date-picker-wrapper .flatpickr-input.unicode-fallback.disabled::after,
.custom-date-picker-wrapper.unicode-fallback.disabled::after {
    color: #adb5bd;
}
```

### Custom Styling

To customize the component appearance, target these CSS classes:

```css
.custom-date-picker-wrapper { /* Main container */ }
.custom-date-picker-wrapper .flatpickr-wrapper { /* Flatpickr wrapper */ }
.custom-date-picker-wrapper .flatpickr-input { /* Input field */ }
.custom-date-picker-wrapper .flatpickr-wrapper::after { /* Chevron icon */ }
.flatpickr-calendar { /* Calendar popup */ }
```

## Configuration

### Flatpickr Configuration

The component uses these default Flatpickr settings:

```json
{
    "dateFormat": "Y-m-d",
    "altInput": true,
    "altFormat": "d/m/Y",
    "allowInput": true,
    "disabled": false
}
```

### Customizing Date Formats

To change the display format, modify the `altFormat` option:

- `"d/m/Y"` = DD/MM/YYYY (European)
- `"d.m.Y"` = DD.MM.YYYY (German style)
- `"d-m-Y"` = DD-MM-YYYY (Alternative European)
- `"j F Y"` = 1 January 2024 (Full text)

The server format (`dateFormat`) should remain `"Y-m-d"` for Laravel compatibility.

## Laravel Validation

### Controller Validation

```php
$request->validate([
    'booking_date' => 'required|date|after_or_equal:today',
]);
```

### Error Display

Validation errors are automatically displayed when using Laravel's validation system:

```blade
<x-custom-date-picker
    name="booking_date"
    id="booking_date"
    label="Date" />
{{-- Error automatically shown if validation fails --}}
```

### Custom Error Bags

For multiple forms or custom error bags:

```blade
<x-custom-date-picker
    name="booking_date"
    id="booking_date"
    error-bag="booking_errors" />
```

## Implementation History

### Original Implementation
- Replaced HTML5 `type="date"` inputs with Flatpickr
- European date format (DD/MM/YYYY) display
- Server-compatible ISO format (YYYY-MM-DD)
- Basic Bootstrap styling

### Enhanced Features
- Manual text input capability
- Past date selection allowed
- Chevron-down icon matching dropdown styling
- Drop shadow container styling
- Enhanced calendar appearance

### Green Theme & Dark Mode
- Bootstrap success green color scheme (#198754)
- Comprehensive dark mode compatibility
- Light grey borders maintained in dark mode
- Enhanced visual hierarchy and contrast

## Migration Guide

### From HTML5 Date Input

**Before:**
```blade
<input type="date" name="booking_date" id="booking_date"
    value="{{ old('booking_date', date('Y-m-d')) }}"
    min="{{ date('Y-m-d') }}" required
    onchange="loadAvailability();"
    class="form-control @error('booking_date') is-invalid @enderror">
```

**After:**
```blade
<x-custom-date-picker
    name="booking_date"
    id="booking_date"
    :value="old('booking_date', date('Y-m-d'))"
    min-date="today"
    :required="true"
    :on-change="'loadAvailability();'" />
```

### Benefits of Migration

1. **Consistent Format**: European date format regardless of browser locale
2. **Enhanced UX**: Calendar picker with manual input capability
3. **Better Styling**: Professional appearance with green theme
4. **Dark Mode**: Automatic theme compatibility
5. **Maintainability**: Single component for all date inputs

## Browser Compatibility

The component works across all modern browsers:
- ✅ Chrome/Chromium (all versions)
- ✅ Firefox (all versions)
- ✅ Safari (all versions)
- ✅ Edge (all versions)
- ✅ Internet Explorer 11+ (with graceful degradation)

## Performance Considerations

- **@once Directives**: CSS and JavaScript included only once per page
- **Lazy Initialization**: Date pickers initialized only when needed
- **Memory Management**: Proper cleanup and reference management
- **Event Delegation**: Efficient event handling for dynamic content

## Testing

### Test Page Features

Access `/test/custom-date-picker` (admin authentication required) to test:

- All component props and configurations
- Manual input in various European formats
- Dark mode toggle functionality
- JavaScript API methods
- Form submission and validation
- Theme switching capabilities

### Test Scenarios

1. **Basic Usage**: Default component behavior
2. **Required Fields**: Validation and error display
3. **Manual Input**: European date format typing
4. **Disabled State**: Non-interactive appearance
5. **Custom Min Date**: Date restriction functionality
6. **Dark Mode**: Theme compatibility
7. **JavaScript API**: Programmatic control

## Troubleshooting

### Common Issues & Solutions

#### 1. **Calendar Icon Not Displaying**

**Symptoms:**
- No icon appears on the right side of the input field
- Empty space where calendar icon should be

**Solutions:**
- **Check Icon Font Loading**: Use the test page (`/test/custom-date-picker`) and click "🔍 Check Icons" to verify font availability
- **Verify Network Loading**: Check browser Network tab for successful loading of `boxicons.woff2`, `tabler-icons.woff2`, etc.
- **CSS Selector Issues**: Use browser developer tools to inspect the `::after` pseudo-element
- **Fallback Application**: Check console for fallback messages like "Applied Tabler Icons fallback"

**Debug Steps:**
```javascript
// Test icon font availability in browser console
const test = document.createElement('span');
test.style.fontFamily = 'boxicons';
test.innerHTML = '&#xf237;';
document.body.appendChild(test);
console.log('Boxicons width:', test.offsetWidth);
document.body.removeChild(test);
```

#### 2. **JavaScript Error: "Invalid date provided"**

**Symptoms:**
- Error message: "Invalid date provided: 2025-08-24"
- Component fails to initialize
- Date picker doesn't open

**Root Cause:** Custom `parseDate` function not handling ISO format dates

**Solution:** This has been fixed in the latest version with enhanced `parseDate` function that handles both ISO (YYYY-MM-DD) and European (DD/MM/YYYY) formats.

**Verification:**
```javascript
// Check if the fix is applied - should see this in console
console.log('Parsing date: 2025-08-24 with format: Y-m-d');
```

#### 3. **Initial Value Not Displaying**

**Symptoms:**
- Component receives `value="2024-12-25"` but shows empty field
- Expected display: "25/12/2024", Actual: Empty field

**Root Cause:** Flatpickr not reading initial value during initialization

**Solution:** Fixed by setting `defaultDate` in configuration:
```javascript
if (initialValue && initialValue.trim() !== '') {
    config.defaultDate = initialValue;
    console.log('Setting defaultDate for', input.id, ':', initialValue);
}
```

**Verification:** Check console for "Setting defaultDate" messages

#### 4. **Date Picker Not Initializing**

**Symptoms:**
- Input field appears as regular text input
- No calendar functionality
- Console errors about Flatpickr

**Solutions:**
- Ensure Flatpickr is loaded before the component
- Check browser console for JavaScript errors
- Verify the component is properly included in the page

#### 5. **Styling Conflicts**

**Symptoms:**
- Calendar appearance doesn't match expected design
- Icons not positioned correctly

**Solutions:**
- Verify CSS specificity and !important declarations
- Check for conflicting Bootstrap or custom styles
- Use browser developer tools to inspect applied styles

#### 6. **Validation Not Working**

**Symptoms:**
- Laravel validation errors not displaying
- Form submission issues

**Solutions:**
- Confirm error bag configuration
- Verify Laravel validation rules
- Check that the `name` attribute matches validation rules

#### 7. **Manual Input Not Parsing**

**Symptoms:**
- Typed dates not being accepted
- Invalid date errors for valid European dates

**Solutions:**
- Check date format (DD/MM/YYYY, DD.MM.YYYY, DD-MM-YYYY)
- Ensure valid date ranges (1900-current year)
- Verify `parseDate` function is working correctly

### Test Page & Debugging Tools

The component includes a comprehensive test page at `/test/custom-date-picker` (admin access required) with the following debugging tools:

#### Icon Font Test Section
Visual verification of icon font availability:
- **Boxicons calendar** (primary): 📅
- **Tabler Icons chevron** (fallback): ⌄
- **Bootstrap Icons chevron** (fallback): ⌄
- **Unicode calendar** (ultimate fallback): 📅

#### Debug Buttons

**🔍 Check Icons**: Tests all icon font availability and reports results:
```
Boxicons (primary): ✅ Available (width: 16px)
Tabler Icons (fallback): ✅ Available (width: 16px)
Bootstrap Icons (fallback): ✅ Available (width: 16px)

Fallbacks Applied:
Picker 1: Default Boxicons
Picker 2: Default Boxicons
```

**🔍 DOM Structure**: Inspects actual Flatpickr DOM structure:
```
=== Date Picker 1 (booking_date) ===
Original Input: INPUT.form-control
Custom Wrapper: Found
Flatpickr Instance: Found
Alt Input: Yes
Alt Input Element: INPUT.flatpickr-input
```

#### Console Debugging

**Successful Initialization:**
```
Setting defaultDate for booking_date : 2024-12-25
Successfully initialized Flatpickr for: booking_date
Boxicons loaded successfully for: booking_date
```

**Error Scenarios:**
```
Error initializing custom date picker for booking_date : Invalid date provided
Boxicons not available, trying Tabler Icons fallback for: booking_date
Applied Tabler Icons fallback for: booking_date
```

### Debug Mode

Enable enhanced console logging for troubleshooting:

```javascript
window.customDatePickerDebug = true;
```

## Conclusion

The custom-date-picker component provides a comprehensive, professional date input solution for Laravel applications. With its intuitive Boxicons calendar icon, European date format, robust error handling, comprehensive fallback system, success green theme, dark mode compatibility, and extensive customization options, it serves as the definitive date picker component for the SMP Online application.

### Key Achievements

✅ **Visual Excellence**: Boxicons calendar icon provides clear, intuitive date picker indication
✅ **Robust Error Handling**: Enhanced `parseDate` function handles both ISO and European formats
✅ **Reliable Initialization**: Fixed initial value display issues with proper `defaultDate` configuration
✅ **Comprehensive Fallbacks**: 4-tier icon fallback system ensures icons always display
✅ **Enhanced Debugging**: Complete test page with diagnostic tools and console logging
✅ **Dark Mode Compatibility**: Full theme support with proper contrast and styling
✅ **Developer Experience**: Extensive documentation, troubleshooting guides, and debugging tools

The component successfully balances functionality, accessibility, and visual appeal while maintaining backward compatibility and providing a superior user experience compared to standard HTML5 date inputs. The recent updates have resolved all known issues and established a robust, maintainable foundation for date input functionality across the application.
