<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class CustomDatePicker extends Component
{
    public string $name;

    public string $id;

    public ?string $value;

    public string $placeholder;

    public bool $required;

    public ?string $minDate;

    public bool $disabled;

    public string $class;

    public ?string $errorBag;

    public ?string $onChange;

    public ?string $label;

    public bool $showLabel;

    /**
     * Create a new component instance.
     */
    public function __construct(
        string $name,
        string $id,
        ?string $value = null,
        string $placeholder = 'DD/MM/YYYY',
        bool $required = false,
        ?string $minDate = null,
        bool $disabled = false,
        string $class = '',
        ?string $errorBag = null,
        ?string $onChange = null,
        ?string $label = null,
        bool $showLabel = true
    ) {
        $this->name = $name;
        $this->id = $id;
        $this->value = $value;
        $this->placeholder = $placeholder;
        $this->required = $required;
        $this->minDate = $minDate;
        $this->disabled = $disabled;
        $this->class = $class;
        $this->errorBag = $errorBag;
        $this->onChange = $onChange;
        $this->label = $label;
        $this->showLabel = $showLabel;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.custom-date-picker');
    }

    /**
     * Get the error message for this field.
     */
    public function getErrorMessage(): ?string
    {
        $errors = session('errors');
        if (! $errors) {
            return null;
        }

        if ($this->errorBag) {
            $errorBag = $errors->getBag($this->errorBag);

            return $errorBag->first($this->name);
        }

        return $errors->first($this->name);
    }

    /**
     * Check if this field has validation errors.
     */
    public function hasError(): bool
    {
        return ! is_null($this->getErrorMessage());
    }

    /**
     * Generate a unique component ID for JavaScript initialization.
     */
    public function getComponentId(): string
    {
        return 'custom-date-picker-'.$this->id;
    }
}
