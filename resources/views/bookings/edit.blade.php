@extends('layouts.admin')

@section('title', 'Edit Booking - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Edit Booking #{{ $booking->id }}</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('bookings.index') }}">Bookings</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('bookings.show', $booking) }}">Booking
                            #{{ $booking->id }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Edit Booking Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-edit me-2"></i>Edit Booking - {{ $booking->field->name }}
                        @php
                            $statusColors = [
                                'Pending' => 'warning',
                                'Confirmed' => 'secondary',
                                'Cancelled' => 'danger',
                                'Completed' => 'primary',
                            ];
                            $statusColor = $statusColors[$booking->status] ?? 'secondary';
                        @endphp
                        <span
                            class="badge bg-{{ $statusColor }}-transparent text-{{ $statusColor }} ms-2">{{ $booking->status }}</span>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('bookings.show', $booking) }}" class="btn btn-info btn-sm">
                            <i class="ti ti-eye me-1"></i>View Booking
                        </a>
                        <a href="{{ route('bookings.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Bookings
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Current Booking Info -->
                    <div class="alert alert-info mb-4">
                        <h6 class="fw-semibold mb-2">Current Booking Information</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Status:</strong> {{ $booking->status }}
                            </div>
                            <div class="col-md-3">
                                <strong>Date:</strong> {{ $booking->booking_date->format('M d, Y') }}
                            </div>
                            <div class="col-md-3">
                                <strong>Time:</strong> {{ $booking->time_range }}
                            </div>
                            <div class="col-md-3">
                                <strong>Cost:</strong> XCG {{ number_format($booking->total_cost, 2) }}
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('bookings.update', $booking) }}" id="editBookingForm">
                        @csrf
                        @method('PUT')

                        <div class="row gy-4">
                            <!-- Field and Date Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Field & Schedule</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Field Selection -->
                                            <div class="col-xl-12">
                                                <label for="field_id" class="form-label">Field <span
                                                        class="text-danger">*</span></label>
                                                <select name="field_id" id="field_id" required
                                                    onchange="updateFieldInfo(); calculateCost();"
                                                    class="form-select @error('field_id') is-invalid @enderror">
                                                    <option value="">Select Field</option>
                                                    @foreach ($fields as $field)
                                                        <option value="{{ $field->id }}"
                                                            data-rate="{{ $field->hourly_rate }}"
                                                            data-capacity="{{ $field->capacity }}"
                                                            data-type="{{ $field->type }}"
                                                            {{ old('field_id', $booking->field_id) == $field->id ? 'selected' : '' }}>
                                                            {{ $field->name }} -
                                                            XCG {{ number_format($field->hourly_rate, 2) }}/hr
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('field_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Field Information Display -->
                                            <div class="col-xl-12">
                                                <div id="fieldInfo" class="alert alert-info">
                                                    <h6 class="fw-semibold">Field Information</h6>
                                                    <p class="mb-1"><strong>Type:</strong> <span
                                                            id="fieldType">{{ $booking->field->type }}</span></p>
                                                    <p class="mb-1"><strong>Capacity:</strong> <span
                                                            id="fieldCapacity">{{ $booking->field->capacity }}</span>
                                                        people</p>
                                                    <p class="mb-0"><strong>Hourly Rate:</strong> XCG <span
                                                            id="fieldRate">{{ number_format($booking->field->hourly_rate, 2) }}</span>
                                                    </p>
                                                </div>
                                            </div>

                                            <!-- Booking Date -->
                                            <div class="col-xl-12">
                                                <label for="booking_date" class="form-label">Date <span
                                                        class="text-danger">*</span></label>
                                                <input type="date" name="booking_date" id="booking_date"
                                                    value="{{ old('booking_date', $booking->booking_date->format('Y-m-d')) }}"
                                                    min="{{ date('Y-m-d') }}" required onchange="calculateCost();"
                                                    class="form-control @error('booking_date') is-invalid @enderror">
                                                @error('booking_date')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Start Time -->
                                            <div class="col-xl-12">
                                                <label for="start_time" class="form-label">Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="start_time" id="start_time" required
                                                    onchange="calculateCost();"
                                                    class="form-select @error('start_time') is-invalid @enderror">
                                                    <option value="">Select Time</option>
                                                    @for ($hour = 8; $hour < 22; $hour++)
                                                        @php
                                                            $time = sprintf('%02d:00', $hour);
                                                            $display = date('g:i A', strtotime($time));
                                                        @endphp
                                                        <option value="{{ $time }}"
                                                            {{ old('start_time', $booking->start_time) == $time ? 'selected' : '' }}>
                                                            {{ $display }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Duration -->
                                            <div class="col-xl-12">
                                                <label for="duration_hours" class="form-label">Duration <span
                                                        class="text-danger">*</span></label>
                                                <select name="duration_hours" id="duration_hours" required
                                                    onchange="calculateCost();"
                                                    class="form-select @error('duration_hours') is-invalid @enderror">
                                                    @for ($i = 1; $i <= 8; $i++)
                                                        <option value="{{ $i }}"
                                                            {{ old('duration_hours', $booking->duration_hours) == $i ? 'selected' : '' }}>
                                                            {{ $i }} {{ Str::plural('hour', $i) }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                @error('duration_hours')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Cost Display -->
                                            <div class="col-xl-12">
                                                <div id="costDisplay" class="alert alert-success">
                                                    <h6 class="fw-semibold">Booking Cost</h6>
                                                    <p class="mb-1"><strong>Total Cost: XCG <span
                                                                id="totalCost">{{ number_format($booking->total_cost, 2) }}</span></strong>
                                                    </p>
                                                    <p class="mb-0 fs-12">Rate: XCG <span
                                                            id="displayRate">{{ number_format($booking->field->hourly_rate, 2) }}</span>/hour
                                                        × <span id="displayDuration">{{ $booking->duration_hours }}</span>
                                                        hour(s)</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Customer Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            @if (auth()->user()->isAdmin())
                                                <div class="col-xl-12">
                                                    <div class="alert alert-warning">
                                                        <strong>Admin Note:</strong> You can modify customer information for
                                                        this booking.
                                                    </div>
                                                </div>
                                            @endif

                                            <!-- Customer Name -->
                                            <div class="col-xl-12">
                                                <label for="customer_name" class="form-label">Customer Name</label>
                                                <input type="text" name="customer_name" id="customer_name"
                                                    value="{{ old('customer_name', $booking->customer_name) }}"
                                                    class="form-control @error('customer_name') is-invalid @enderror">
                                                @error('customer_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Email -->
                                            <div class="col-xl-12">
                                                <label for="customer_email" class="form-label">Customer Email</label>
                                                <input type="email" name="customer_email" id="customer_email"
                                                    value="{{ old('customer_email', $booking->customer_email) }}"
                                                    class="form-control @error('customer_email') is-invalid @enderror">
                                                @error('customer_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Phone -->
                                            <div class="col-xl-12">
                                                <label for="customer_phone" class="form-label">Customer Phone</label>
                                                <input type="tel" name="customer_phone" id="customer_phone"
                                                    value="{{ old('customer_phone', $booking->customer_phone) }}"
                                                    class="form-control @error('customer_phone') is-invalid @enderror">
                                                @error('customer_phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Special Requests -->
                                            <div class="col-xl-12">
                                                <label for="special_requests" class="form-label">Special Requests</label>
                                                <textarea name="special_requests" id="special_requests" rows="3"
                                                    class="form-control @error('special_requests') is-invalid @enderror"
                                                    placeholder="Any special requirements or notes...">{{ old('special_requests', $booking->special_requests) }}</textarea>
                                                @error('special_requests')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Booking Status Info -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-info">
                                                    <h6 class="fw-semibold">Booking Status</h6>
                                                    <p class="mb-1"><strong>Current Status:</strong>
                                                        {{ $booking->status }}
                                                    </p>
                                                    <p class="mb-1"><strong>Created:</strong>
                                                        {{ $booking->created_at->format('M d, Y H:i') }}</p>
                                                    @if ($booking->confirmed_at)
                                                        <p class="mb-0"><strong>Confirmed:</strong>
                                                            {{ $booking->confirmed_at->format('M d, Y H:i') }}</p>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-between mt-4 pt-3 border-top">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ti ti-check me-1"></i>Update Booking
                                        </button>
                                        <a href="{{ route('bookings.show', $booking) }}" class="btn btn-secondary">
                                            <i class="ti ti-x me-1"></i>Cancel
                                        </a>
                                    </div>

                                    @if ($booking->canBeCancelled() && (auth()->user()->isAdmin() || $booking->user_id === auth()->id()))
                                        <button type="button" class="btn btn-danger"
                                            onclick="confirmCancelBooking({{ $booking->id }}, '{{ $booking->field->name }} - {{ $booking->booking_date->format('M j, Y') }}')">
                                            <i class="ti ti-x me-1"></i>Cancel Booking
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Confirmation Modal -->
    <x-confirmation-modal
        modal-id="cancelBookingModal"
        type="warning"
        title="Are you sure you want to cancel the booking for &quot;<span id='bookingDetails' class='fw-semibold'></span>&quot;?"
        warning-text="This action cannot be undone. The booking will be permanently cancelled."
        cancel-text="No, Keep Booking"
        confirm-text="Yes, Cancel Booking"
        form-action="#"
        form-method="POST"
    />

    @push('scripts')
        <script>
            function updateFieldInfo() {
                const fieldSelect = document.getElementById('field_id');
                const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];

                if (selectedOption.value) {
                    document.getElementById('fieldType').textContent = selectedOption.dataset.type || 'N/A';
                    document.getElementById('fieldCapacity').textContent = selectedOption.dataset.capacity || 'N/A';
                    document.getElementById('fieldRate').textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);
                    calculateCost();
                }
            }

            function calculateCost() {
                const fieldSelect = document.getElementById('field_id');
                const duration = document.getElementById('duration_hours').value;
                const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];

                if (selectedOption.value && duration) {
                    const rate = parseFloat(selectedOption.dataset.rate || 0);
                    const total = rate * parseInt(duration);

                    document.getElementById('totalCost').textContent = total.toFixed(2);
                    document.getElementById('displayRate').textContent = rate.toFixed(2);
                    document.getElementById('displayDuration').textContent = duration;
                }
            }

            // Cancel booking confirmation function
            function confirmCancelBooking(bookingId, bookingDetails) {
                document.getElementById('bookingDetails').textContent = bookingDetails;
                document.getElementById('cancelBookingModalForm').action = `/bookings/${bookingId}/cancel`;
                const modal = new bootstrap.Modal(document.getElementById('cancelBookingModal'));
                modal.show();
            }

            // Initialize on page load
            document.addEventListener('DOMContentLoaded', function() {
                updateFieldInfo();
                calculateCost();
            });
        </script>
    @endpush
@endsection
